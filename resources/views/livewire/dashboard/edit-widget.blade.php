<div class="edit-widget-container" wire:key="edit-widget-{{ $widgetId }}">
    <div class="d-flex">
        {{-- Collapsible Left Sidebar --}}
        {{-- Use sidebarCollapsed property to control 'collapsed' class --}}
        <div class="left-sidebar @if($sidebarCollapsed) collapsed @endif">
            {{-- Sidebar Toggle Button --}}
            {{-- Use wire:click to toggle Livewire property --}}
            <button class="sidebar-toggle" wire:click="toggleSidebarState" title="Toggle Sidebar">
                <i class="fas fa-bars"></i>
            </button>

            {{-- Tab Navigation --}}
            <ul class="nav nav-tabs sidebar-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link @if($activeTab === 'settings') active @endif"
                       id="settings-tab"
                       href="#settings"
                       role="tab"
                       wire:click.prevent="$set('activeTab', 'settings')">
                        <i class="fas fa-cog"></i>
                        <span class="tab-text">Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @if($activeTab === 'sorting') active @endif"
                       id="sorting-tab"
                       href="#sorting"
                       role="tab"
                       wire:click.prevent="$set('activeTab', 'sorting')">
                        <i class="fas fa-sort"></i>
                        <span class="tab-text">Sorting</span>
                    </a>
                </li>
            </ul>

            {{-- Tab Content --}}
            <div class="tab-content sidebar-content">
                {{-- Settings Tab --}}
                <div class="tab-pane fade @if($activeTab === 'settings') show active @endif" id="settings" role="tabpanel">
                    <form wire:submit.prevent="saveWidget" enctype="multipart/form-data" id="widget-form">
                        <h5 class="mb-3 font-weight-bold">Widget Settings</h5>

                        <div class="form-group mb-3">
                            <label for="widget_title" class="font-weight-bold">Widget Name</label>
                            <input type="text" class="form-control" id="widget_title" wire:model.live.debounce.500ms="widget_title"
                                   placeholder="Enter widget name" required>
                            @error('widget_title') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="pool_selection" class="font-weight-bold">Pool</label>
                            <input type="hidden" name="pool_type" wire:model="pool_type">

                            <div class="position-relative" wire:click.away="closePoolDropdown" x-data="{ showDropdown: @entangle('showPoolDropdown') }">
                                <div class="form-control d-flex justify-content-between align-items-center cursor-pointer"
                                     wire:click="togglePoolDropdown"
                                     id="pool_selection_display">
                                    {{ $selectedPoolName ?: 'Select Pool' }}
                                    <span class="caret-down" :class="{ 'rotate': showDropdown }">&#9660;</span>
                                </div>

                                @if($showPoolDropdown)
                                    <div class="card p-2 position-absolute w-100 mt-1 shadow-sm" style="z-index: 10;">
                                        <input type="text" class="form-control mb-2" placeholder="Search pools..."
                                               wire:model.live.debounce.300ms="searchPoolTerm"
                                               x-ref="searchPoolInput"
                                               wire:keydown.escape="closePoolDropdown">

                                        <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                            @forelse($pools as $pool)
                                                <button type="button" class="list-group-item list-group-item-action py-2"
                                                        wire:click="selectPool({{ $pool->id }}, '{{ $pool->pool_name }}')">
                                                    {{ $pool->pool_name }}
                                                </button>
                                            @empty
                                                <p class="list-group-item text-center text-muted">No pools found.</p>
                                            @endforelse
                                        </div>
                                    </div>
                                @endif
                            </div>
                            @error('pool_type') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="diagram_type" class="font-weight-bold">Diagram Type</label>
                            <select id="diagram_type" class="form-control" wire:model.live="diagram_type" required>
                                <option value="">Select a type</option>
                                @foreach (\App\Enums\DiagramType::cases() as $type)
                                    <option value="{{ strtolower($type->value) }}">{{ $type->label() }}</option>
                                @endforeach
                            </select>
                            @error('diagram_type') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>

                        {{-- Removed Widget Width and Height Inputs --}}

                        <div class="form-group mb-3" wire:key="file-upload-section">
                            <label for="chakra_image" class="font-weight-bold d-block mb-1">Upload Image</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="chakra_image" wire:model="chakra_image" accept="image/*">
                                <label class="custom-file-label" for="chakra_image">
                                    Choose file
                                </label>
                            </div>
                            @error('chakra_image') <span class="text-danger">{{ $message }}</span> @enderror

                            <div wire:loading wire:target="chakra_image" class="mt-2">
                                <small class="text-muted">
                                    <span class="spinner-border spinner-border-sm mr-1"></span>
                                    Uploading image...
                                </small>
                            </div>
                        </div>

                        @if ($temporaryImageUrl)
                            <div class="mb-3">
                                <img src="{{ $temporaryImageUrl }}" id="chakra_image_preview"
                                     class="img-fluid border rounded" style="max-height: 150px;" alt="New Image" wire:key="temp-img-preview">
                            </div>
                        @elseif ($exist_chakra_image)
                            <div class="mb-3">
                                <img src="{{ Storage::url('images/' . $exist_chakra_image) }}" id="chakra_image_preview"
                                     class="img-fluid border rounded" style="max-height: 150px;" alt="Chakra Image" wire:key="exist-img-preview-{{ $exist_chakra_image }}">
                            </div>
                        @endif

                        <div class="form-group mb-4">
                            <label for="image_position" class="font-weight-bold">Image Position</label>
                            <select id="image_position" class="form-control" wire:model.live="image_position">
                                <option value="left">Left</option>
                                <option value="right">Right</option>
                            </select>
                            @error('image_position') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>

                        <div class="form-group mb-4">
                            <label for="image_max_width" class="font-weight-bold">Image Max Width (%)</label>
                            <div class="input-group">
                                <input type="number"
                                       id="image_max_width"
                                       class="form-control"
                                       wire:model.live="image_max_width"
                                       {{-- min="5"
                                       max="80" --}}
                                       step="1"
                                       placeholder="50">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                Recommended: 30% or 20% for better iPad display (768px). Default is 50%.
                            </small>
                            @error('image_max_width') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>

                        <div class="action-buttons">
                            <button type="button" class="btn btn-secondary btn-block mb-2" onclick="window.close()">
                                <i class="fas fa-times mr-1"></i> Close Editor
                            </button>
                            <button type="submit" class="btn btn-primary btn-block" wire:loading.attr="disabled">
                                <span wire:loading.remove><i class="fas fa-save mr-1"></i> Save Widget</span>
                                <span wire:loading><span class="spinner-border spinner-border-sm mr-1"></span> Processing...</span>
                            </button>
                        </div>
                    </form>
                </div>

                {{-- Sorting Tab --}}
                <div class="tab-pane fade @if($activeTab === 'sorting') show active @endif" id="sorting" role="tabpanel">
                    <h5 class="mb-3 font-weight-bold">Analysis Sorting</h5>
                    <div class="sorting-info alert alert-info py-2 px-3 mb-3">
                        <i class="fas fa-info-circle mr-2"></i>
                        <small>Drag and drop to reorder analyses</small>
                    </div>
                    <div class="pd-details-items-wrapper border rounded" style="overflow-y: auto; max-height: calc(100vh - 300px);">
                        <ul class="list-group" id="module-head-pool-sort">
                            @forelse ($analyses as $key => $value)
                                <li class="list-group-item d-flex align-items-center" data-id="{{ $key }}">
                                    <i class="fas fa-grip-vertical text-muted mr-2"></i>
                                    {{ $value }}
                                </li>
                            @empty
                                <li class="list-group-item text-warning text-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    No analysis found!
                                </li>
                            @endforelse
                        </ul>
                    </div>
                    <input type="hidden" name="sorted_ids" wire:model.defer="sorted_ids">
                </div>
            </div>
        </div>

        {{-- Preview Area --}}
        <div class="preview-area" id="previewArea">
            <div class="preview-panel">
                <div class="preview-header">
                    <i class="fas fa-eye mr-2"></i>Live Preview
                    <div wire:loading wire:target="loadPreviewData,updateSortedIdsLive,chakra_image" class="ml-auto">
                        <span class="spinner-border spinner-border-sm text-white"></span>
                    </div>
                </div>
                <div class="preview-body">
                    @if($previewData)
                        @php
                            $diagramType = strtolower($previewData['diagram_type'] ?? '');
                            // Reverted to fixed heights for preview based on original CSS
                            $previewFixedGraphHeight = 350; // default for full graph
                            $previewFixedImageGraphHeight = 300; // default for graph with image
                        @endphp

                        @if (in_array($diagramType, ['bar', 'line', 'radar', 'polararea', 'progress','progressbarsmall']))
                            <div class="preview-widget">
                                <div class="widget-title">{{ $previewData['title'] }}</div>

                                @if (!empty($previewData['settings']['image']))
                                    @php
                                        $imageMaxWidth = (int) (is_numeric($image_max_width) ? $image_max_width : ($previewData['settings']['image_max_width'] ?? 50));
                                        $graphWidth = 100 - $imageMaxWidth;
                                    @endphp
                                    <div class="preview-content-with-image {{ $previewData['settings']['image_position'] === 'right' ? '' : 'reverse' }}"
                                         style="height: {{ $previewFixedImageGraphHeight }}px;">
                                        <div class="preview-graph-container" style="height: 100%; flex: 0 0 {{ $graphWidth }}%; max-width: {{ $graphWidth }}%;">
                                            <div wire:key="preview-graph-{{ $previewData['id'] }}">
                                                @includeIf('Frontend.dashboard.partials._graph', [
                                                    'widget' => $previewData,
                                                    'diagramType' => $diagramType,
                                                    'isPreview' => true
                                                ])
                                            </div>
                                        </div>
                                        <div class="preview-image-wrapper" style="flex: 0 0 {{ $imageMaxWidth }}%; max-width: {{ $imageMaxWidth }}%; width: auto;">
                                            <img src="{{ $previewData['settings']['image'] }}" alt="Preview Image" wire:key="preview-img-{{ $previewData['id'] }}">
                                        </div>
                                    </div>
                                @else
                                    <div class="preview-graph-full" style="height: {{ $previewFixedGraphHeight }}px;">
                                        <div wire:key="preview-graph-{{ $previewData['id'] }}">
                                            @includeIf('Frontend.dashboard.partials._graph', [
                                                'widget' => $previewData,
                                                'diagramType' => $diagramType,
                                                'isPreview' => true
                                            ])
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @elseif ($diagramType === 'biorythm')
                            <div class="preview-placeholder" style="min-height: {{ $previewFixedGraphHeight }}px;">
                                <i class="fas fa-wave-square fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Biorhythm preview not available</p>
                            </div>
                        @elseif ($diagramType === 'menu')
                            <div class="preview-placeholder" style="min-height: {{ $previewFixedGraphHeight }}px;">
                                <i class="fas fa-bars fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Menu preview not available</p>
                            </div>
                        @elseif ($diagramType === 'richtext')
                            <div class="preview-placeholder" style="min-height: {{ $previewFixedGraphHeight }}px;">
                                <livewire:jodit-text-editor wire:model.live="richTextContent" />
                                <link rel="stylesheet" href="//unpkg.com/jodit@4.1.16/es2021/jodit.min.css">
                                <script src="//unpkg.com/jodit@4.1.16/es2021/jodit.min.js"></script>
                                <br>
                                <button type="button"
                                        class="btn btn-primary"
                                        x-on:click="$wire.call('saveContent')"
                                        wire:loading.attr="disabled">
                                    <span wire:loading.remove><i class="fas fa-save mr-1"></i>Save Content</span>
                                    <span wire:loading><span class="spinner-border spinner-border-sm mr-1"></span> Processing...</span>
                                </button>
                            </div>
                        @else
                            <div class="preview-placeholder">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <p class="text-muted mb-0">Select a diagram type to preview</p>
                                <small class="text-muted">Select a pool and diagram type to get started</small>
                            </div>
                        @endif
                    @else
                        <div class="preview-placeholder">
                            <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Configure your widget to see a live preview</p>
                            <small class="text-muted">Select a pool and diagram type to get started</small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
    <style>
        /* Container */
        .edit-widget-container {
            height: 100vh;
            overflow: hidden;
        }

        /* Layout */
        .edit-widget-container > .d-flex {
            height: 100%;
        }

        /* Left Sidebar */
        .left-sidebar {
            width: 400px;
            background: #ffffff;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            position: relative;
            transition: width 0.3s ease, margin-left 0.3s ease;
            overflow: hidden; /* Keep hidden when collapsed */
        }

        .left-sidebar.collapsed {
            width: 60px;
        }

        /* Sidebar Toggle Button */
        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .left-sidebar.collapsed .sidebar-toggle {
            right: 12px;
        }

        /* Sidebar Tabs */
        .sidebar-tabs {
            margin-top: 60px;
            border-bottom: 1px solid #e2e8f0;
            padding: 0 15px;
            flex-shrink: 0;
        }

        .left-sidebar.collapsed .sidebar-tabs {
            padding: 0 5px;
        }

        .sidebar-tabs .nav-link {
            color: #718096;
            border: none;
            border-radius: 0;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar-tabs .nav-link i {
            font-size: 18px;
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-tabs .nav-link.active {
            color: #667eea;
            background: transparent;
            border-bottom: 2px solid #667eea;
        }

        .sidebar-tabs .nav-link:hover {
            color: #667eea;
            background: #f7fafc;
        }

        .left-sidebar.collapsed .tab-text {
            display: none;
        }

        .left-sidebar.collapsed .sidebar-tabs .nav-link {
            padding: 12px 8px;
            justify-content: center;
        }

        /* Sidebar Content */
        .sidebar-content {
            flex: 1;
            overflow-y: auto; /* Ensures scroll if content exceeds */
            padding: 20px;
        }

        .left-sidebar.collapsed .sidebar-content {
            padding: 20px 10px;
            opacity: 0;
            visibility: hidden;
        }

        /* Preview Area */
        .preview-area {
            flex: 1;
            background: #f8f9fa;
            overflow: hidden; /* This should be handled by inner elements */
            display: flex;
            flex-direction: column;
        }

        .preview-panel {
            height: 100%; /* Important for flex to work down the chain */
            display: flex;
            flex-direction: column;
            background: #ffffff;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            flex-shrink: 0;
        }

        .preview-body {
            flex: 1; /* Allow body to take available space */
            padding: 40px;
            background: #f8f9fa;
            overflow-y: auto; /* Enable scrolling for preview content */
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column; /* To center content vertically if needed */
        }

        .preview-widget {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Prevents widget content from spilling out horizontally */
        }

        .widget-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
        }

        .preview-content-with-image {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1; /* Allow this to take height */
        }

        .preview-content-with-image.reverse {
            flex-direction: row-reverse;
        }

        .preview-graph-container {
            flex: 1;
            height: 100%; /* Take full height of parent */
            min-width: 0; /* Important for flex items */
        }

        .preview-graph-full {
            width: 100%;
            flex: 1; /* Allow this to take height */
        }

        .preview-image-wrapper {
            min-width: 120px;
            flex-shrink: 0; /* Do not shrink image wrapper */
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
        }

        .preview-image-wrapper img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-placeholder {
            text-align: left; /* Change from center to left */
            padding: 60px;
            display: flex;
            flex-direction: column;
            align-items: flex-start; /* Change from center to flex-start */
            justify-content: flex-start; /* Change from center to flex-start */
            min-height: 350px; /* Base min-height */
        }

        /* Form styling */
        h5.font-weight-bold {
            color: #2d3748;
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .form-group label {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 6px;
        }

        .form-control {
            border-color: #e2e8f0;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.1);
        }

        /* Action Buttons */
        .action-buttons {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        /* Sorting Section */
        .sorting-info {
            background: #e6f3ff;
            border: 1px solid #b3d9ff;
            font-size: 13px;
        }

        .pd-details-items-wrapper {
            background: #f8f9fa;
        }

        #module-head-pool-sort .list-group-item {
            cursor: move;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        #module-head-pool-sort .list-group-item:hover {
            background-color: #f1f3f5;
            border-left-color: #667eea;
        }

        #module-head-pool-sort .ui-sortable-helper {
            background-color: #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-left-color: #667eea;
        }

        /* Custom select dropdown */
        .cursor-pointer {
            cursor: pointer;
        }

        .caret-down {
            transition: transform 0.3s ease;
            font-size: 12px;
        }

        .caret-down.rotate {
            transform: rotate(180deg);
        }

        /* Responsive */
        @media (max-width: 991px) {
            .left-sidebar {
                width: 100%;
                max-width: 400px;
                position: absolute;
                height: 100%;
                z-index: 100;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .left-sidebar.collapsed {
                margin-left: -340px;
            }

            .preview-area {
                width: 100%;
            }

            .preview-body {
                padding: 20px;
            }

            .preview-widget {
                max-width: 100%;
                padding: 20px;
            }

            .preview-content-with-image {
                flex-direction: column !important;
                height: auto; /* Reset height */
            }

            .preview-image-wrapper {
                width: 100%;
                height: 150px;
                margin-top: 15px;
            }

            .preview-graph-container,
            .preview-graph-full {
                height: 250px; /* Fallback for smaller screens if dynamic height is too large */
            }
        }

        @media (max-width: 575px) {
            .sidebar-toggle {
                width: 30px;
                height: 30px;
                font-size: 14px;
            }

            .preview-body {
                padding: 15px;
            }

            .preview-widget {
                padding: 15px;
            }

            .widget-title {
                font-size: 16px;
            }
        }

        /* Add specific style for the Jodit editor container */
        .jodit-container {
            width: 100%;
            margin-left: 0;
        }
    </style>

    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <script>
        document.addEventListener('livewire:initialized', () => {
            // Initial call to set up sorting
            initializeSorting();

            // Re-initialize sorting after Livewire updates the DOM
            Livewire.hook('morph.updated', ({ el, component }) => {
                if (el.id === 'module-head-pool-sort' || el.querySelector('#module-head-pool-sort')) {
                    setTimeout(() => {
                        initializeSorting();
                    }, 10); // Small delay to ensure DOM is fully ready
                }
            });

            // Listen for 'poolChanged' event
            Livewire.on('poolChanged', () => {
                // No need to explicitly call initializeSorting here as morph.updated hook will catch the change
            });

            // Listen for 'widgetSaved' event
            Livewire.on('widgetSaved', () => {
                // You could add a browser notification here if desired
            });

            // Event listener for the custom file input label
            document.addEventListener('change', function (e) {
                if (e.target && e.target.id === 'chakra_image') {
                    let fileName = e.target.files[0]?.name ?? 'Choose file';
                    let label = e.target.nextElementSibling;
                    if (label) {
                        label.innerText = fileName;
                    }
                }
            });

            // Focus the search input when the dropdown is opened
            Livewire.on('poolDropdownOpened', () => {
                setTimeout(() => {
                    if (Livewire.first() && Livewire.first().$refs.searchPoolInput) {
                        Livewire.first().$refs.searchPoolInput.focus();
                    }
                }, 50);
            });

            // --- Sidebar Toggle Logic ---
            const sidebar = document.querySelector('.left-sidebar'); // Select by class, as id might not be static on morph
            // Sync initial sidebar state from localStorage to Livewire component
            const storedCollapsedState = localStorage.getItem('sidebarCollapsed');
            if (storedCollapsedState !== null) {
                const isCollapsed = storedCollapsedState === 'true';
                Livewire.dispatch('setSidebarCollapsedState', { collapsed: isCollapsed });
            }

            // Listen for Livewire to tell us to update localStorage
            Livewire.on('updateLocalStorageSidebar', ({ collapsed }) => {
                localStorage.setItem('sidebarCollapsed', collapsed);
            });
            // --- End Sidebar Toggle Logic ---
        });


        /**
         * Initializes the jQuery UI Sortable functionality on the analysis list.
         */
        function initializeSorting() {
            const sortableList = $('#module-head-pool-sort');

            // Destroy existing sortable instance before re-initializing to prevent duplicates
            if (sortableList.data('uiSortable')) {
                sortableList.sortable('destroy');
            }

            if (sortableList.length) {
                sortableList.sortable({
                    update: function (event, ui) {
                        const sortedIdList = $(this).sortable('toArray', {attribute: 'data-id'});
                        Livewire.dispatch('updateSortedIdsLive', {sortedIds: sortedIdList});
                    }
                });
            }
        }
    </script>
@endsection